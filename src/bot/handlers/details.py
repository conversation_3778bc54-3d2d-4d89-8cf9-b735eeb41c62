"""
EduGuideBot v3 Details Handler
Handles detailed view of major recommendations and navigation
"""

import logging
from typing import Dict, Any, List, Optional
from telegram import Update, InlineKeyboardMarkup, InlineKeyboardButton, CallbackQuery
from telegram.ext import ContextTypes, CallbackQueryHandler

from src.bot.telegram_safe_v3 import safe_edit_message, safe_answer_callback, safe_send_message, log_telegram_errors
from src.core.data.loader import get_university_by_id

logger = logging.getLogger(__name__)

# Assessment state constant
ASSESSMENT_STATE = 1


def get_university_data_by_id(university_id: str) -> Optional[Dict]:
    """Get university data by ID from real data files"""
    try:
        from pathlib import Path
        import json

        data_dir = Path("data/raw")

        # Search through all JSON files for university data
        for file_path in data_dir.rglob("*.json"):
            try:
                with open(file_path, "r", encoding="utf-8") as f:
                    data = json.load(f)

                # Handle both list and dict formats
                items = data if isinstance(data, list) else [data]

                for item in items:
                    # Check if this is a university object with matching ID
                    if "university" in item and "majors" in item:
                        university = item["university"]

                        # Extract university info
                        name_kh = university.get('name_kh', '')
                        name_en = university.get('name_en', university.get('name', ''))
                        uni_id = university.get('id', '')

                        # Check if university_id matches
                        if (university_id.lower() in file_path.stem.lower() or
                            university_id.lower() == uni_id.lower() or
                            university_id.lower() in name_kh.lower() or
                            university_id.lower() in name_en.lower()):

                            # Extract contact and location info
                            contact = university.get('contact', {})
                            location = university.get('location', {})

                            # Filter out fake contact info
                            phone_list = contact.get('phone', [])
                            real_phone = None
                            if isinstance(phone_list, list):
                                for phone in phone_list:
                                    if phone and not phone.endswith('000 000') and not phone.endswith('123456789'):
                                        real_phone = phone
                                        break
                                if not real_phone and phone_list:
                                    real_phone = phone_list[0]  # Use first phone if no real one found
                            else:
                                real_phone = phone_list if phone_list and not phone_list.endswith('000 000') else None

                            # Filter out fake email
                            email = contact.get('email', '')
                            if not email or 'example' in email or email == '<EMAIL>':
                                email = 'មិនទាន់មានទិន្នន័យ'

                            # Filter out fake website
                            website = university.get('website', '')
                            if not website or 'example' in website or website == 'https://university.edu.kh':
                                website = 'មិនទាន់មានទិន្នន័យ'

                            # Get social media
                            social_media = contact.get('social_media', {})
                            facebook = social_media.get('facebook', '') if social_media.get('facebook') else 'មិនទាន់មានទិន្នន័យ'

                            return {
                                'name': name_kh or name_en,
                                'name_kh': name_kh,
                                'name_en': name_en,
                                'city': location.get('city', 'ភ្នំពេញ'),
                                'address': location.get('address_kh', location.get('address_en', 'ភ្នំពេញ, កម្ពុជា')),
                                'phone': real_phone or 'មិនទាន់មានទិន្នន័យ',
                                'email': email,
                                'website': website,
                                'facebook': facebook,
                                'latitude': location.get('latitude', 11.5564),
                                'longitude': location.get('longitude', 104.9282),
                                'majors': item.get('majors', []),
                                'founding_year': university.get('founding_year', 'មិនទាន់មានទិន្នន័យ'),
                                'type': university.get('type', 'មិនទាន់មានទិន្នន័យ')
                            }
            except Exception as e:
                logger.error(f"Error reading {file_path}: {e}")
                continue

        return None

    except Exception as e:
        logger.error(f"Error getting university data: {e}")
        return None


@log_telegram_errors
async def show_major_details(update: Update, context: ContextTypes.DEFAULT_TYPE) -> Optional[int]:
    """Show detailed information about a recommended major"""
    try:
        query = update.callback_query
        await safe_answer_callback(query)

        # Extract major ID from callback data
        if not isinstance(query.data, str) or not query.data.startswith('details_'):
            logger.error(f"Invalid callback data in show_major_details: {query.data}")
            await safe_edit_message(
                query,
                text="❌ សូមអភ័យទោស! មិនអាចរកមុខវិជ្ជាសិក្សាបាន។ សូមព្យាយាមម្តងទៀត។"
            )
            return ASSESSMENT_STATE

        _, major_id = query.data.split('_', 1)

        # Get cached recommendations
        recommendations = context.user_data.get('recommendations', [])

        # Find selected program in cached recommendations
        selected_rec = None
        for rec in recommendations:
            if rec.get('major_id') == major_id:
                selected_rec = rec
                break

        if not selected_rec:
            # Try to find in all programs using the working data loader
            try:
                from src.core.data_loader import load_raw
                all_programs = load_raw()
                for prog in all_programs:
                    if prog.get('major_id') == major_id:
                        selected_rec = prog
                        break
            except Exception as e:
                logger.error(f"Error loading programs for details: {e}")

        # Log for debugging
        logger.info(f"Looking for major_id: {major_id}")
        logger.info(f"Found in recommendations: {selected_rec is not None}")
        if not selected_rec and recommendations:
            sample_ids = [rec.get('major_id', 'N/A') for rec in recommendations[:3]]
            logger.info(f"Sample major_ids in recommendations: {sample_ids}")

        if not selected_rec:
            await safe_edit_message(
                query,
                text="❌ សូមអភ័យទោស! មិនអាចរកមុខវិជ្ជាសិក្សាបាន។ សូមព្យាយាមម្តងទៀត។"
            )
            return ASSESSMENT_STATE
            
        # Build comprehensive message content
        if context.user_data.get('lang', 'kh') == 'kh':
            message_parts = [
                f"<b>📌 ព័ត៌មានលម្អិតនៃជំនាញ:</b>",
                f"🎓 <b>ជំនាញ:</b> {selected_rec.get('major_name_kh', selected_rec.get('major_name', 'មិនទាន់មានទិន្នន័យ'))}",
                f"🏫 <b>សាកលវិទ្យាល័យ:</b> {selected_rec.get('university_name_kh', selected_rec.get('university_name', 'មិនទាន់មានទិន្នន័យ'))}"
            ]
        else:
            message_parts = [
                f"<b>📌 Major Details:</b>",
                f"🎓 <b>Major:</b> {selected_rec.get('major_name_en', selected_rec.get('major_name', 'N/A'))}",
                f"🏫 <b>University:</b> {selected_rec.get('university_name_en', selected_rec.get('university_name', 'N/A'))}"
            ]

        # Add location
        location = selected_rec.get('city', selected_rec.get('location', 'មិនទាន់មានទីតាំង'))
        message_parts.append(f"📍 <b>ទីតាំង:</b> {location}")

        # Add comprehensive fees information
        fees_usd = selected_rec.get('tuition_fees_usd', selected_rec.get('fees_usd', 'មិនទាន់មានទិន្នន័យ'))
        fees_khr = selected_rec.get('tuition_fees_khr', selected_rec.get('fees_khr', 'មិនទាន់មានទិន្នន័យ'))

        # Check if fees are valid (not empty, not 'N/A', not 0, not None)
        valid_usd = fees_usd not in ['មិនទាន់មានទិន្នន័យ', 'N/A', '', 0, None]
        valid_khr = fees_khr not in ['មិនទាន់មានទិន្នន័យ', 'N/A', '', 0, None]

        if valid_usd or valid_khr:
            fee_parts = []
            if valid_usd:
                fee_parts.append(f"${fees_usd}")
            if valid_khr:
                fee_parts.append(f"៛{fees_khr}")
            message_parts.append(f"💰 <b>ថ្លៃសិក្សា:</b> {' / '.join(fee_parts)}")
        else:
            message_parts.append(f"💰 <b>ថ្លៃសិក្សា:</b> មិនមានទិន្នន័យតម្លៃសិក្សា")

        # Add duration
        duration = selected_rec.get('duration_years', selected_rec.get('duration', 'មិនទាន់មានទិន្នន័យ'))
        message_parts.append(f"📚 <b>រយៈពេលសិក្សា:</b> {duration} ឆ្នាំ")

        # Add employment rate with comprehensive handling
        employment_rate = selected_rec.get('employment_rate', 'មិនទាន់មានទិន្នន័យ')
        try:
            if isinstance(employment_rate, str) and employment_rate.replace('%', '').strip().isdigit():
                rate = int(employment_rate.replace('%', '').strip())
                stars = '★' * min(5, max(1, round(rate/20))) + '☆' * max(0, 5 - round(rate/20))
                message_parts.append(f"📈 <b>អត្រារកការងារ:</b> {stars} ({rate}%)")
            elif isinstance(employment_rate, (int, float)):
                rate = int(employment_rate)
                stars = '★' * min(5, max(1, round(rate/20))) + '☆' * max(0, 5 - round(rate/20))
                message_parts.append(f"📈 <b>អត្រារកការងារ:</b> {stars} ({rate}%)")
            else:
                message_parts.append(f"📈 <b>អត្រារកការងារ:</b> ★★★☆☆")
        except (ValueError, TypeError):
            message_parts.append(f"📈 <b>អត្រារកការងារ:</b> ★★★☆☆")

        # Add MCDA score explanation
        mcda_score = selected_rec.get('mcda_score', 0)
        if isinstance(mcda_score, (int, float)) and mcda_score > 0:
            mcda_stars = '★' * round(mcda_score * 5) + '☆' * (5 - round(mcda_score * 5))
            message_parts.append(f"📊 <b>MCDA ពិន្ទុ:</b> {mcda_stars} ({mcda_score * 100:.0f}% ត្រឹមត្រូវ)")

        # Add ML confidence rating
        ml_score = selected_rec.get('ml_score', 0)
        if isinstance(ml_score, (int, float)) and ml_score > 0:
            star_count = round(ml_score * 5)
            ml_stars = '★' * star_count + '☆' * (5 - star_count)
            message_parts.append(f"🤖 <b>បញ្ជាក់ដោយ AI:</b> {ml_stars} ({ml_score * 100:.0f}% ជឿជាក់)")

        # Add internship availability
        has_internship = selected_rec.get('internship_required', selected_rec.get('internship_availability', False))
        if isinstance(has_internship, bool):
            internship_text = 'បាន' if has_internship else 'គ្មាន'
        else:
            internship_text = str(has_internship) if has_internship else 'គ្មាន'
        message_parts.append(f"🎓 <b>ការហ្វឹកហាត់:</b> {internship_text}")

        # Add description if available
        description = selected_rec.get('description_kh', selected_rec.get('description_en', selected_rec.get('description', '')))
        if description and description != 'មិនទាន់មានទិន្នន័យ':
            # Truncate long descriptions
            if len(description) > 200:
                description = description[:200] + "..."
            message_parts.append(f"\n📝 <b>ការពិពណ៌នា:</b>\n{description}")

        # Add career prospects if available
        career_prospects = selected_rec.get('career_prospects_kh', selected_rec.get('career_prospects_en', ''))
        if career_prospects and career_prospects != 'មិនទាន់មានទិន្នន័យ':
            if len(career_prospects) > 150:
                career_prospects = career_prospects[:150] + "..."
            message_parts.append(f"\n💼 <b>ឱកាសការងារ:</b>\n{career_prospects}")

        # Add requirements if available
        requirements = selected_rec.get('requirements', [])
        if requirements and isinstance(requirements, list) and len(requirements) > 0:
            req_text = ", ".join(requirements[:3])  # Show first 3 requirements
            message_parts.append(f"\n📋 <b>តម្រូវការ:</b> {req_text}")

        # Create enhanced keyboard with multiple options including PDF export
        keyboard = [
            [
                InlineKeyboardButton("📞 ទំនាក់ទំនង", callback_data=f"contact_{selected_rec.get('university_id', 'unknown')}"),
                InlineKeyboardButton("📍 ទីតាំង", callback_data=f"location_{selected_rec.get('university_id', 'unknown')}")
            ],
            [
                InlineKeyboardButton("📄 ទាញយក PDF", callback_data=f"pdf_export_{major_id}")
            ],
            [
                InlineKeyboardButton("🔙 ត្រឡប់ទៅបញ្ជី", callback_data="back_to_recommendations")
            ]
        ]

        reply_markup = InlineKeyboardMarkup(keyboard)

        # Send the message
        await safe_edit_message(
            query=query,
            text='\n'.join(message_parts),
            reply_markup=reply_markup,
            parse_mode='HTML'
        )

        return ASSESSMENT_STATE
            
    except Exception as e:
        logger.error(f"Error in show_major_details: {e}", exc_info=True)
        await safe_edit_message(
            query=query,
            text="❌ សូមអភ័យទោស! មានបញ្ហាក្នុងការបង្ហាញព័ត៌មានលម្អិត។ សូមព្យាយាមម្តងទៀត។",
            reply_markup=None
        )
        return None


@log_telegram_errors
async def back_to_recommendations(update: Update, context: ContextTypes.DEFAULT_TYPE) -> Optional[int]:
    """Return to the main recommendation list - FIXED VERSION"""
    try:
        query = update.callback_query
        await safe_answer_callback(query)

        # Get cached recommendations
        recommendations = context.user_data.get('recommendations', [])

        if not recommendations:
            await safe_edit_message(
                query,
                text="❌ សូមអភ័យទោស! មានបញ្ហាក្នុងការបង្ហាញអនុសាសន៍។ សូមចាប់ផ្ដើមការវាយតម្លៃម្តងទៀត។",
                reply_markup=None
            )
            return ASSESSMENT_STATE

        # Create STATIC recommendations display - NO LOOPS
        rec_text = "🎯 ការណែនាំសម្រាប់អ្នក\n\n"
        rec_text += "ខាងក្រោមនេះជាមុខជំនាញដែលសមស្របបំផុតសម្រាប់អ្នក:\n\n"

        for i, rec in enumerate(recommendations[:5], 1):
            name_kh = rec.get('major_name_kh', rec.get('major_name_en', 'មិនមានឈ្មោះ'))
            uni_kh = rec.get('university_name_kh', rec.get('university_name_en', 'មិនមានឈ្មោះ'))
            location = rec.get('city', rec.get('location', 'មិនមានទីតាំង'))
            fees = rec.get('tuition_fees_usd', rec.get('fees_usd', 'N/A'))

            # Get hybrid score safely
            hybrid_score = rec.get('hybrid_score', rec.get('score', 0.5))
            confidence = get_confidence_stars(hybrid_score)

            rec_text += f"{i}. {name_kh}\n"
            rec_text += f"🏫 {uni_kh}\n"
            rec_text += f"📍 {location}\n"
            if fees not in ['N/A', '', 0, None]:
                rec_text += f"💰 ${fees} USD\n"
            else:
                rec_text += f"💰 មិនមានទិន្នន័យតម្លៃសិក្សា\n"
            rec_text += f"★ {confidence} ({hybrid_score * 100:.0f}% ត្រូវគ្នា)\n\n"

        # Create action buttons for each recommendation - SAME AS ORIGINAL
        keyboard_buttons = []
        for i, rec in enumerate(recommendations[:5]):
            major_id = rec.get('major_id', f'unknown_{i}')
            keyboard_buttons.append([
                InlineKeyboardButton(f"🔍 ព័ត៌មានបន្ថែម #{i+1}", callback_data=f"details_{major_id}")
            ])

        keyboard_buttons.append([
            InlineKeyboardButton("🔄 ធ្វើតេស្តម្តងទៀត", callback_data="restart_assessment")
        ])

        from telegram import InlineKeyboardMarkup
        keyboard = InlineKeyboardMarkup(keyboard_buttons)

        # CRITICAL: Just edit the message, don't call any other handlers
        await safe_edit_message(query, rec_text, keyboard)

        logger.info("Successfully returned to recommendations list")
        return ASSESSMENT_STATE

    except Exception as e:
        logger.error(f"Error in back_to_recommendations: {e}", exc_info=True)
        await safe_edit_message(
            query,
            text="❌ សូមអភ័យទោស! មានបញ្ហាក្នុងការត្រឡប់ទៅបញ្ជី។ សូមព្យាយាមម្តងទៀត។",
            reply_markup=None
        )
        return ASSESSMENT_STATE


@log_telegram_errors
async def show_university_contact(update: Update, context: ContextTypes.DEFAULT_TYPE) -> Optional[int]:
    """Show university contact information"""
    try:
        query = update.callback_query
        await safe_answer_callback(query)

        # Extract university_id from callback data
        # Format: contact_uni_<university_id> or contact_<university_id>
        parts = query.data.split("_")
        if len(parts) >= 3 and parts[1] == "uni":
            university_id = "_".join(parts[2:])  # contact_uni_rupp_majors -> rupp_majors
        else:
            university_id = "_".join(parts[1:])  # contact_rupp -> rupp

        # Get university details from patched data
        university = get_university_data_by_id(university_id)

        if not university:
            await safe_edit_message(
                query,
                text="❌ សូមអភ័យទោស! មិនអាចរកឃើញព័ត៌មានទំនាក់ទំនងសាកលវិទ្យាល័យ។",
                reply_markup=InlineKeyboardMarkup([[
                    InlineKeyboardButton("🔙 ត្រឡប់ទៅបញ្ជី", callback_data="back_to_recommendations")
                ]])
            )
            return ASSESSMENT_STATE

        # Build contact message
        contact_text = f"""
📞 <b>ព័ត៌មានទំនាក់ទំនងសាកលវិទ្យាល័យ</b>

🏫 <b>ឈ្មោះ:</b> {university.get('name', 'មិនទាន់មានទិន្នន័យ')}
📍 <b>ទីតាំង:</b> {university.get('city', 'មិនទាន់មានទិន្នន័យ')}
🏠 <b>អាសយដ្ឋាន:</b> {university.get('address', 'មិនទាន់មានទិន្នន័យ')}
📞 <b>លេខទូរស័ព្ទ:</b> {university.get('phone', 'មិនទាន់មានទិន្នន័យ')}
📧 <b>អ៊ីម៉ែល:</b> {university.get('email', 'មិនទាន់មានទិន្នន័យ')}
🌐 <b>គេហទំព័រ:</b> {university.get('website', 'មិនទាន់មានទិន្នន័យ')}
📘 <b>ហ្វេសប៊ុក:</b> {university.get('facebook', 'មិនទាន់មានទិន្នន័យ')}
"""

        keyboard = InlineKeyboardMarkup([[
            InlineKeyboardButton("🔙 ត្រឡប់ទៅបញ្ជី", callback_data="back_to_recommendations")
        ]])

        await safe_edit_message(query, contact_text, keyboard, parse_mode='HTML')
        return ASSESSMENT_STATE

    except Exception as e:
        logger.error(f"Error showing university contact: {e}", exc_info=True)
        await safe_edit_message(
            query,
            text="❌ សូមអភ័យទោស! មានបញ្ហាក្នុងការបង្ហាញព័ត៌មានទំនាក់ទំនង។",
            reply_markup=InlineKeyboardMarkup([[
                InlineKeyboardButton("🔙 ត្រឡប់ទៅបញ្ជី", callback_data="back_to_recommendations")
            ]])
        )
        return ASSESSMENT_STATE


@log_telegram_errors
async def show_university_location(update: Update, context: ContextTypes.DEFAULT_TYPE) -> Optional[int]:
    """Show university location information"""
    try:
        query = update.callback_query
        await safe_answer_callback(query)

        # Extract university_id from callback data
        # Format: location_uni_<university_id> or location_<university_id>
        parts = query.data.split("_")
        if len(parts) >= 3 and parts[1] == "uni":
            university_id = "_".join(parts[2:])  # location_uni_rupp_majors -> rupp_majors
        else:
            university_id = "_".join(parts[1:])  # location_rupp -> rupp

        # Get university details from patched data
        university = get_university_data_by_id(university_id)

        if not university:
            await safe_edit_message(
                query,
                text="❌ សូមអភ័យទោស! មិនអាចរកឃើញព័ត៌មានទីតាំងសាកលវិទ្យាល័យ។",
                reply_markup=InlineKeyboardMarkup([[
                    InlineKeyboardButton("🔙 ត្រឡប់ទៅបញ្ជី", callback_data="back_to_recommendations")
                ]])
            )
            return ASSESSMENT_STATE

        # Generate Google Maps link from coordinates
        latitude = university.get('latitude', 11.5564)  # Default to Phnom Penh center
        longitude = university.get('longitude', 104.9282)
        google_maps_url = f"https://maps.google.com/?q={latitude},{longitude}"

        # Build location message
        location_text = f"""
📍 <b>ព័ត៌មានទីតាំងសាកលវិទ្យាល័យ</b>

🏫 <b>ឈ្មោះ:</b> {university.get('name', 'មិនទាន់មានទិន្នន័យ')}
🏙️ <b>ទីក្រុង:</b> {university.get('city', 'មិនទាន់មានទិន្នន័យ')}
🏠 <b>អាសយដ្ឋានពេញលេញ:</b> {university.get('address', 'មិនទាន់មានទិន្នន័យ')}
🗺️ <b>ទីតាំងនៃសាកលវិទ្យាល័យ:</b> <a href="{google_maps_url}">Google Maps</a>

📚 <b>មុខជំនាញដែលមាន:</b> {len(university.get('majors', []))} មុខជំនាញ
"""

        keyboard = InlineKeyboardMarkup([[
            InlineKeyboardButton("🔙 ត្រឡប់ទៅបញ្ជី", callback_data="back_to_recommendations")
        ]])

        await safe_edit_message(query, location_text, keyboard, parse_mode='HTML')
        return ASSESSMENT_STATE

    except Exception as e:
        logger.error(f"Error showing university location: {e}", exc_info=True)
        await safe_edit_message(
            query,
            text="❌ សូមអភ័យទោស! មានបញ្ហាក្នុងការបង្ហាញព័ត៌មានទីតាំង។",
            reply_markup=InlineKeyboardMarkup([[
                InlineKeyboardButton("🔙 ត្រឡប់ទៅបញ្ជី", callback_data="back_to_recommendations")
            ]])
        )
        return ASSESSMENT_STATE



@log_telegram_errors
async def generate_pdf_export(update: Update, context: ContextTypes.DEFAULT_TYPE) -> Optional[int]:
    """Generate PDF version of major details"""
    try:
        query = update.callback_query
        await safe_answer_callback(query)

        # Extract major index from callback data
        # Format: pdf_export_<index> or pdf_<index>
        parts = query.data.split("_")
        if len(parts) >= 3 and parts[1] == "export":
            major_index = int(parts[2])  # pdf_export_0 -> 0
        else:
            major_index = int(parts[1])  # pdf_0 -> 0

        # Get cached recommendations
        recommendations = context.user_data.get('recommendations', [])

        # Find selected program by index
        if not recommendations or major_index >= len(recommendations):
            await safe_edit_message(
                query,
                text="❌ សូមអភ័យទោស! មិនអាចរកមុខវិជ្ជាសិក្សាបាន។ សូមព្យាយាមម្តងទៀត។",
                reply_markup=InlineKeyboardMarkup([[
                    InlineKeyboardButton("🔙 ត្រឡប់ទៅបញ្ជី", callback_data="back_to_recommendations")
                ]])
            )
            return ASSESSMENT_STATE

        selected_rec = recommendations[major_index]

        # Extract program data from recommendation
        program = selected_rec.get('program', selected_rec)

        # Create comprehensive PDF content
        pdf_content = f"""
📌 ព័ត៌មានលម្អិតនៃមុខវិជ្ជាសិក្សា

🎓 ជំនាញ: {program.get('major_name_kh', program.get('name_kh', program.get('name', 'មិនទាន់មានទិន្នន័យ')))}
🏫 សាកលវិទ្យាល័យ: {program.get('university_name_kh', program.get('university_kh', 'មិនទាន់មានទិន្នន័យ'))}
📍 ទីតាំង: {program.get('city', program.get('location', 'មិនទាន់មានទីតាំង'))}
💰 ថ្លៃសិក្សា: ៛{program.get('tuition_fees_khr', program.get('fees_khr', 'មិនទាន់មានទិន្នន័យ'))} / ${program.get('tuition_fees_usd', program.get('fees_usd', 'មិនទាន់មានទិន្នន័យ'))}
📚 រយៈពេលសិក្សា: {program.get('study_duration_kh', program.get('duration_years', '៤ ឆ្នាំ'))}
📈 អត្រារកការងារ: {program.get('employment_rate', 'មិនទាន់មានទិន្នន័យ')}
📊 MCDA ពិន្ទុ: {selected_rec.get('mcda_score', 0) * 100:.0f}% ត្រឹមត្រូវ
🤖 AI ពិន្ទុ: {selected_rec.get('ml_score', 0) * 100:.0f}% ជឿជាក់
🎯 ពិន្ទុរួម: {selected_rec.get('hybrid_score', 0) * 100:.0f}% ត្រូវគ្នា
🎓 ការហ្វឹកហាត់: {'បាន' if program.get('internship_required', False) else 'គ្មាន'}

📝 ការពិពណ៌នាមុខជំនាញ:
{program.get('description_kh', program.get('description_en', program.get('description', 'មុខជំនាញនេះផ្តល់ការអប់រំគុណភាពខ្ពស់')))}

💼 ឱកាសការងារ:
{program.get('career_prospects_kh', program.get('career_prospects_en', 'មានឱកាសការងារល្អនៅក្នុងវិស័យនេះ'))}

📋 តម្រូវការចូលរៀន:
{', '.join(program.get('entry_requirements_kh', program.get('requirements', ['សញ្ញាបត្រមធ្យមសិក្សាទុតិយភូមិ'])))}

🏛️ ព័ត៌មានសាកលវិទ្យាល័យ:
ប្រភេទ: {program.get('university_type', 'ឯកជន')}
ឆ្នាំបង្កើត: {program.get('founding_year', 'មិនទាន់មានទិន្នន័យ')}

---
បង្កើតដោយ EduGuideBot v3 - ប្រព័ន្ធណែនាំសាកលវិទ្យាល័យ
ថ្ងៃបង្កើត: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""

        # Send as a text message (simulating PDF export)
        await safe_send_message(
            update,
            context,
            text=f"📄 <b>ព័ត៌មានលម្អិតនៃមុខវិជ្ជាសិក្សា (ទម្រង់ PDF)</b>\n\n<pre>{pdf_content}</pre>",
            parse_mode='HTML'
        )

        # Also provide a back button
        keyboard = InlineKeyboardMarkup([[
            InlineKeyboardButton("🔙 ត្រឡប់ទៅបញ្ជី", callback_data="back_to_recommendations")
        ]])

        await safe_edit_message(
            query,
            text="✅ ព័ត៌មានលម្អិតត្រូវបានបង្កើតជាទម្រង់ PDF ខាងលើ។",
            reply_markup=keyboard
        )

        return ASSESSMENT_STATE

    except Exception as e:
        logger.error(f"PDF generation failed: {e}", exc_info=True)
        await safe_edit_message(
            query,
            text="❌ សូមអភ័យទោស! មានបញ្ហាក្នុងការបង្កើត PDF។ សូមព្យាយាមម្តងទៀត។",
            reply_markup=InlineKeyboardMarkup([[
                InlineKeyboardButton("🔙 ត្រឡប់ទៅបញ្ជី", callback_data="back_to_recommendations")
            ]])
        )
        return ASSESSMENT_STATE


def get_confidence_stars(score: float) -> str:
    """Convert score to star rating."""
    if score >= 0.8:
        return "★★★★★"
    elif score >= 0.6:
        return "★★★★☆"
    elif score >= 0.4:
        return "★★★☆☆"
    else:
        return "★★☆☆☆"


def register_handlers(application):
    """Register handlers for detail view and navigation"""
    from telegram.ext import CallbackQueryHandler

    application.add_handler(CallbackQueryHandler(show_major_details, pattern="^details_"))
    application.add_handler(CallbackQueryHandler(show_university_contact, pattern="^contact_"))
    application.add_handler(CallbackQueryHandler(show_university_location, pattern="^location_"))

    application.add_handler(CallbackQueryHandler(generate_pdf_export, pattern="^pdf_"))
    application.add_handler(CallbackQueryHandler(back_to_recommendations, pattern="^back_to_recommendations$"))
